<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Marketing Frame Template</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: "Segoe UI", Tahoma, sans-serif;
      background: linear-gradient(135deg, #eceff1, #f5f5f5);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }

    .frame-container {
      width: 650px;
      background: #fff;
      border-radius: 20px;
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    /* Brand Bar */
    .bottom-brand {
      display: flex;
      align-items: center;
      padding: 15px 25px;
      background: linear-gradient(to right, #fff, #f9f9f9);
      border-bottom: 2px solid #f0f0f0;
    }
    .bottom-brand img {
      max-height: 55px;
      margin-right: 15px;
    }
    .bottom-brand span {
      font-size: 26px;
      font-weight: 700;
      letter-spacing: 1px;
      color: #a8211b;
    }

    /* Main Image */
    .center-placeholder {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 25px;
      border-radius: 15px;
      min-height: 300px;
      transition: all 0.3s ease;
    }
    .center-placeholder img {
      max-width: 80%;
      max-height: 80%;
      object-fit: contain;
      border-radius: 12px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .center-placeholder img:hover {
      transform: scale(1.03);
      box-shadow: 0 6px 18px rgba(0,0,0,0.15);
    }

    /* Logo Bar */
    .top-logos {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 18px 20px;
      background: #f9fafb;
      border-top: 2px solid #f0f0f0;
    }
    .top-logos img {
      max-height: 45px;
      object-fit: contain;
      opacity: 0.85;
      transition: opacity 0.3s ease;
    }
    .top-logos img:hover {
      opacity: 1;
    }

    /* Snapshot Button Styling */
    .snapshot-btn {
      margin-top: 20px;
      padding: 12px 24px;
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      border: none;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
      transition: all 0.3s ease;
    }
    .snapshot-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
      background: linear-gradient(135deg, #45a049, #3d8b40);
    }
    .snapshot-btn:active {
      transform: translateY(0);
    }

    /* Loading indicator */
    .loading {
      opacity: 0.7;
      pointer-events: none;
    }

    /* Placeholder for missing images */
    .img-placeholder {
      width: 100px;
      height: 50px;
      background: #f0f0f0;
      border: 2px dashed #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #666;
      border-radius: 8px;
    }
  </style>
</head>
<body>
  <div class="frame-container" id="frame-container">

    <!-- Company Branding -->
    <div class="bottom-brand">
      <img src="brand lgo/triumph logo.png" alt="Triumph Enterprises Logo" onload="console.log('Triumph logo loaded')" onerror="console.log('Failed to load:', this.src)">
      <span>Triumph Enterprises</span>
    </div>
    
    <!-- Center Placeholder (Insert your marketing image here) -->
    <div class="center-placeholder">
      <img src="brand lgo/test.jpeg" alt="Marketing Image" onload="console.log('Main image loaded')" onerror="console.log('Failed to load:', this.src)">
    </div>

    <!-- Partner Logos -->
    <div class="top-logos">
      <img src="brand lgo/rehau.jpg" alt="Rehau Logo" onload="console.log('Rehau loaded')" onerror="console.log('Failed to load:', this.src)">
      <img src="brand lgo/qubo.png" alt="Qubo Logo" onload="console.log('Qubo loaded')" onerror="console.log('Failed to load:', this.src)">
      <img src="brand lgo/tagus.png" alt="Tagus Logo" onload="console.log('Tagus loaded')" onerror="console.log('Failed to load:', this.src)">
      <img style="max-height: 30px;" src="brand lgo/haier.png" alt="Haier Logo" onload="console.log('Haier loaded')" onerror="console.log('Failed to load:', this.src)">
      <img style="max-height: 60px;" src="brand lgo/hardwyn.jpg" alt="Hardwyn Logo" onload="console.log('Hardwyn loaded')" onerror="console.log('Failed to load:', this.src)">
    </div>
  </div>

  <div class="server-instructions" style="margin-top: 20px; padding: 20px; background: #fff3cd; border-radius: 10px; border-left: 4px solid #ffc107; max-width: 650px;">
    <h3 style="margin-top: 0; color: #856404;">⚠️ To Enable Snapshot Feature</h3>
    <p style="margin-bottom: 10px; color: #856404;"><strong>The snapshot feature requires a local web server due to browser security (CORS).</strong></p>
    
    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
      <h4 style="margin-top: 0; color: #495057;">Quick Setup Options:</h4>
      
      <p><strong>Option 1 - Python (if installed):</strong></p>
      <code style="background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-family: 'Courier New', monospace;">python -m http.server 8000</code>
      <p style="margin: 5px 0; font-size: 14px;">Then visit: <code>http://localhost:8000/your-file.html</code></p>
      
      <p><strong>Option 2 - Node.js (if installed):</strong></p>
      <code style="background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-family: 'Courier New', monospace;">npx http-server</code>
      
      <p><strong>Option 3 - PHP (if installed):</strong></p>
      <code style="background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-family: 'Courier New', monospace;">php -S localhost:8000</code>
      
      <p><strong>Option 4 - Use VS Code:</strong></p>
      <p style="font-size: 14px;">Install "Live Server" extension, then right-click HTML file → "Open with Live Server"</p>
    </div>
    
    <p style="margin-bottom: 0; color: #856404; font-size: 14px;"><strong>Once running on HTTP (not file://), the snapshot feature will work perfectly!</strong></p>
  </div>

  <button class="snapshot-btn" id="snapshotBtn" onclick="takeSnapshot()">📸 Save Snapshot</button>

  <!-- Import html2canvas (works fine with HTTP server) -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script>
    // Wait for all images to load
    function waitForImages() {
      return new Promise((resolve) => {
        const images = document.querySelectorAll('#frame-container img');
        let loadedCount = 0;
        const totalImages = images.length;
        
        if (totalImages === 0) {
          resolve();
          return;
        }
        
        function checkComplete() {
          loadedCount++;
          if (loadedCount === totalImages) {
            resolve();
          }
        }
        
        images.forEach(img => {
          if (img.complete) {
            checkComplete();
          } else {
            img.addEventListener('load', checkComplete);
            img.addEventListener('error', checkComplete);
          }
        });
      });
    }

    // Convert image to base64 to avoid CORS issues
    function imageToBase64(img) {
      return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = img.naturalWidth || img.width;
        canvas.height = img.naturalHeight || img.height;
        
        try {
          ctx.drawImage(img, 0, 0);
          const dataURL = canvas.toDataURL('image/png');
          resolve(dataURL);
        } catch (error) {
          // If image can't be converted, use a placeholder
          resolve('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjUwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iNTAiIGZpbGw9IiNmMGYwZjAiLz48dGV4dCB4PSI1MCIgeT0iMjUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuMzVlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjNjY2Ij5JbWFnZTwvdGV4dD48L3N2Zz4=');
        }
      });
    }

    // Convert all images to base64 to prevent taint
    async function convertImagesToBase64() {
      const images = document.querySelectorAll('#frame-container img');
      const conversions = [];
      
      for (let img of images) {
        conversions.push(
          imageToBase64(img).then(base64 => {
            img.src = base64;
          })
        );
      }
      
      await Promise.all(conversions);
      // Wait a bit for images to update
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    async function takeSnapshot() {
      const frame = document.getElementById("frame-container");
      const btn = document.getElementById("snapshotBtn");
      
      // Store original image sources
      const images = document.querySelectorAll('#frame-container img');
      const originalSources = Array.from(images).map(img => img.src);
      
      try {
        btn.textContent = "📸 Converting images...";
        btn.classList.add("loading");
        
        // Convert all images to base64 first
        await convertImagesToBase64();
        
        btn.textContent = "📸 Generating snapshot...";
        
        // Wait for rendering
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Now capture with base64 images (no CORS issues)
        const canvas = await html2canvas(frame, {
          scale: 2,
          useCORS: false,
          allowTaint: true,
          backgroundColor: '#ffffff',
          logging: false
        });
        
        // Create and trigger download
        const dataURL = canvas.toDataURL("image/png", 1.0);
        const link = document.createElement("a");
        link.download = `marketing-frame-${new Date().getTime()}.png`;
        link.href = dataURL;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        btn.textContent = "✅ Saved!";
        setTimeout(() => {
          btn.textContent = "📸 Save Snapshot";
        }, 2000);
        
      } catch (error) {
        console.error("Error generating snapshot:", error);
        btn.textContent = "❌ Snapshot failed";
        setTimeout(() => {
          btn.textContent = "📸 Save Snapshot";
        }, 3000);
      } finally {
        // Restore original image sources
        images.forEach((img, index) => {
          if (originalSources[index]) {
            img.src = originalSources[index];
          }
        });
        
        btn.classList.remove("loading");
      }
    }
    
    // Additional helper function for future image management
    function replaceImage(selector, newSrc) {
      const img = document.querySelector(selector);
      if (img) {
        img.src = newSrc;
      }
    }
  </script>
</body>
</html>