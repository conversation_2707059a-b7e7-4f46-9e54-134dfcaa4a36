<!-- <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Marketing Frame Template</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: sans-serif;
      background: #f5f5f5;
    }
    .frame-container {
      width: 800px;
      margin: 30px auto;
      background: white;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      position: relative;
    }
    .top-logos, .bottom-brand {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 15px 20px;
      background: #eaeaea;
    }
    .top-logos img {
      max-height: 40px;
      margin: 0 15px;
      object-fit: contain;
    }
    .center-placeholder {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
      border: 2px dashed #ccc;
      margin: 20px;
      background: #fafafa;
    }
    .bottom-brand img {
      max-height: 50px;
      margin-right: 15px;
    }
    .bottom-brand span {
      font-size: 24px;
      font-weight: bold;
      color: #333;
    }
  </style>
</head>
<body>
  <div class="frame-container">
    <div class="bottom-brand">
      <img src="brand lgo\triumph logo.png" alt="Triumph Enterprises Logo">
      <span style="color: #a8211b;">Triumph Enterprises</span>
    </div>
    
    <div class="center-placeholder">
      <img src="brand lgo\indpen.png" alt="">
    </div>
    <div class="top-logos">
     
    </div>
  </div>
</body>
</html> -->

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Triumph Enterprises - Premium Marketing Template</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
  .brand {
      max-height: 30px;
      margin: 0 15px;
      object-fit: contain;
    }
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      animation: backgroundShift 10s ease-in-out infinite alternate;
    }

    @keyframes backgroundShift {
      0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      50% { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
      100% { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    }

    .frame-container {
      max-width: 900px;
      width: 100%;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      box-shadow: 
        0 32px 64px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.3);
      position: relative;
      overflow: hidden;
      animation: containerFloat 6s ease-in-out infinite;
    }

    @keyframes containerFloat {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-10px) rotate(0.5deg); }
    }

    .frame-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24, #f0932b, #ff6b6b);
      background-size: 200% 100%;
      animation: gradientMove 3s linear infinite;
    }

    @keyframes gradientMove {
      0% { background-position: 0% 0%; }
      100% { background-position: 200% 0%; }
    }

    .brand-header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 30px 40px;
      background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      position: relative;
      overflow: hidden;
    }

    .brand-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .brand-logo {
      width: 60px;
      height: 60px;
      background: #a8211b;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      box-shadow: 0 8px 32px rgba(168, 33, 27, 0.3);
      animation: logoPulse 2s ease-in-out infinite;
      position: relative;
      overflow: hidden;
    }

    @keyframes logoPulse {
      0%, 100% { transform: scale(1); box-shadow: 0 8px 32px rgba(168, 33, 27, 0.3); }
      50% { transform: scale(1.05); box-shadow: 0 12px 48px rgba(168, 33, 27, 0.5); }
    }

    .brand-logo::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255,255,255,0.3);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: ripple 2s infinite;
    }

    @keyframes ripple {
      0% { width: 0; height: 0; opacity: 1; }
      100% { width: 100px; height: 100px; opacity: 0; }
    }

    .brand-logo span {
      color: white;
      font-size: 24px;
      font-weight: bold;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .brand-title {
      font-size: 36px;
      font-weight: 800;
      background: linear-gradient(45deg, #ffffff, #e8f4ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
      animation: textGlow 2s ease-in-out infinite alternate;
    }

    @keyframes textGlow {
      0% { filter: brightness(1); }
      100% { filter: brightness(1.2); }
    }

    .content-section {
      padding: 40px;
      min-height: 500px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    }

    .content-placeholder {
      width: 100%;
      height: 400px;
      border: 3px dashed #ddd;
      border-radius: 16px;
      background: linear-gradient(135deg, #f8f9ff 0%, #e8f4ff 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      transition: all 0.3s ease;
      overflow: hidden;
    }

    .content-placeholder:hover {
      transform: scale(1.02);
      border-color: #4facfe;
      box-shadow: 0 16px 48px rgba(79, 172, 254, 0.2);
    }

    .content-placeholder::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: conic-gradient(from 0deg, transparent, rgba(79, 172, 254, 0.1), transparent);
      animation: rotate 4s linear infinite;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .content-placeholder:hover::before {
      opacity: 1;
    }

    @keyframes rotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .placeholder-content {
      text-align: center;
      z-index: 1;
      position: relative;
    }

    .placeholder-icon {
      font-size: 64px;
      color: #4facfe;
      margin-bottom: 20px;
      animation: iconFloat 3s ease-in-out infinite;
    }

    @keyframes iconFloat {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    .placeholder-text {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
    }

    .placeholder-subtext {
      font-size: 16px;
      color: #666;
    }

    .partner-logos {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      padding: 30px 40px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      gap: 30px;
      position: relative;
    }

    .partner-logos::before {
      content: 'TRUSTED PARTNERS';
      position: absolute;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 12px;
      font-weight: 600;
      color: #6c757d;
      letter-spacing: 2px;
    }

    .logo-item {
      width: 120px;
      height: 80px;
      background: white;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 20px rgba(0,0,0,0.05);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .logo-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.1), transparent);
      transition: all 0.5s ease;
    }

    .logo-item:hover {
      transform: translateY(-8px) scale(1.05);
      box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }

    .logo-item:hover::before {
      left: 100%;
    }

    .logo-placeholder {
      width: 80px;
      height: 50px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      font-size: 12px;
      text-align: center;
    }

    .decorative-elements {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      overflow: hidden;
    }

    .circle {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(118, 75, 162, 0.1));
      animation: float 6s ease-in-out infinite;
    }

    .circle:nth-child(1) {
      width: 100px;
      height: 100px;
      top: 10%;
      left: 5%;
      animation-delay: 0s;
    }

    .circle:nth-child(2) {
      width: 60px;
      height: 60px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }

    .circle:nth-child(3) {
      width: 80px;
      height: 80px;
      bottom: 20%;
      left: 15%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(180deg); }
    }

    @media (max-width: 768px) {
      .frame-container {
        margin: 10px;
        border-radius: 16px;
      }
      
      .brand-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
      }
      
      .brand-logo {
        margin-right: 0;
        margin-bottom: 15px;
      }
      
      .brand-title {
        font-size: 28px;
      }
      
      .content-section {
        padding: 20px;
      }
      
      .content-placeholder {
        height: 300px;
      }
      
      .partner-logos {
        padding: 20px;
        gap: 15px;
      }
      
      .logo-item {
        width: 100px;
        height: 70px;
      }
    }
  </style>
</head>
<body>
  <div class="frame-container">
    <div class="decorative-elements">
      <div class="circle"></div>
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    
    <div class="brand-header">
      <div class="brand-logo">
        <span>T</span>
      </div>
      <h1 class="brand-title">TRIUMPH ENTERPRISES</h1>
    </div>
    
    <div class="content-section">
      <div class="content-placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">🎯</div>
              <img  src="brand lgo\indpen.png" alt="">
        </div>
      </div>
    </div>
    
    <div class="partner-logos">
      <div class="logo-item">
      <img class="brand" src="brand lgo/qubo.png" alt="Qubo Logo">
     
      </div>
      <div class="logo-item">
        <img class="brand" src="brand lgo/rehau.jpg" alt="Rehau Logo">
     
      </div>
      <div class="logo-item">
        <img class="brand" src="brand lgo/haier.png" alt="Haier Logo">
     
      </div>
      <div class="logo-item">
        <img class="brand" src="brand lgo/tagus.png" alt="Tagus Logo">
     
      </div>
      <div class="logo-item">
        <img class="brand" src="brand lgo/godrej-logo.png" alt="Godrej Logo">
      </div>
    </div>
  </div>

  <script>
    // Add interactive hover effects
    document.addEventListener('DOMContentLoaded', function() {
      const logoItems = document.querySelectorAll('.logo-item');
      
      logoItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        
        item.addEventListener('mouseenter', function() {
          this.style.animation = 'none';
          this.style.transform = 'translateY(-8px) scale(1.05) rotate(2deg)';
        });
        
        item.addEventListener('mouseleave', function() {
          this.style.transform = '';
        });
      });

      // Add parallax effect to decorative circles
      document.addEventListener('mousemove', function(e) {
        const circles = document.querySelectorAll('.circle');
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;
        
        circles.forEach((circle, index) => {
          const speed = (index + 1) * 0.5;
          const x = (mouseX - 0.5) * speed * 20;
          const y = (mouseY - 0.5) * speed * 20;
          circle.style.transform = `translate(${x}px, ${y}px)`;
        });
      });

      // Add click animation to content placeholder
      const placeholder = document.querySelector('.content-placeholder');
      placeholder.addEventListener('click', function() {
        this.style.animation = 'none';
        setTimeout(() => {
          this.style.animation = '';
        }, 100);
      });
    });
  </script>
</body>
</html>