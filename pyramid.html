<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Marketing Frame Template</title>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>

  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: "Segoe UI", Tahoma, sans-serif;
      background: linear-gradient(135deg, #eceff1, #f5f5f5);
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }

    .frame-container {
      width: 650px;
      background: #fff;
      border-radius: 20px;
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    /* Brand Bar */
    .bottom-brand {
      display: flex;
      align-items: center;
      padding: 15px 25px;
      background: linear-gradient(to right, #fff, #f9f9f9);
      border-bottom: 2px solid #f0f0f0;
    }
    .bottom-brand img {
      max-height: 55px;
      margin-right: 15px;
    }
    .bottom-brand span {
      font-size: 26px;
      font-weight: 700;
      letter-spacing: 1px;
      color: #feca0a;
    }
     .download-btn {
      margin: 20px;
      padding: 10px 20px;
      font-size: 16px;
      border: none;
      background-color: #a8211b;
      color: white;
      border-radius: 8px;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .download-btn:hover {
      background-color: #861a15;
    }

    /* Main Image */
    .center-placeholder {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 25px;
      border-radius: 15px;
      /* background: #fafafa; */
      /* border: 3px dashed #ddd; */
      transition: all 0.3s ease;
    }
    .center-placeholder img {
      max-width: 80%;
      max-height: 80%;
      object-fit: contain;
      border-radius: 12px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .center-placeholder img:hover {
      transform: scale(1.03);
      box-shadow: 0 6px 18px rgba(0,0,0,0.15);
    }

    /* Logo Bar */
    .top-logos {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 18px 20px;
      background: #f9fafb;
      border-top: 2px solid #f0f0f0;
    }
    .top-logos img {
      max-height: 45px;
      object-fit: contain;
      opacity: 0.85;
      transition: opacity 0.3s ease;
    }
    .top-logos img:hover {
      opacity: 1;
    }
  </style>
</head>
<body>
  <div class="frame-container" id="frame">

    <!-- Company Branding -->
    <div class="bottom-brand">
      <img src="pyramid/pyramid-logo.png" alt="">
      <span>Pyramid Impex International</span>
    </div>
    
    <!-- Center Placeholder (Insert your marketing image here) -->
    <div class="center-placeholder">
      <img src="brand lgo/festiv.jpeg" alt="Marketing Image">
    </div>

    <!-- Partner Logos -->
    <div class="top-logos">
        <img src="pyramid/rehau.jpg" >
      <img src="pyramid/logo 1.png" >
      <img src="pyramid/logo2.png" >
      <img  src="pyramid/logo3.png" >
      <img src="pyramid/logo4.png" >
      <img  src="pyramid/logo5.png" >
    </div>
  </div>

   <button class="download-btn" onclick="downloadImage()">Download as Image</button>

  <!-- JavaScript for downloading frame-container as image -->
  <script>
    function downloadImage() {
      const frame = document.getElementById('frame');

      html2canvas(frame).then(canvas => {
        const link = document.createElement('a');
        link.download = 'marketing-frame.png';
        link.href = canvas.toDataURL('image/png');
        link.click();
      });
    }
  </script>
</body>
</html>
